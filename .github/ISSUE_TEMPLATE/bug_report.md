---
name: Bug Report
about: Create a report to help us improve
title: '[BUG] '
labels: bug
---

## Bug Description
A clear and concise description of the bug.

### Steps to Reproduce
1. API endpoint called '...'
2. With payload '....'
3. Environment variables set to '....'
4. See error

### Expected Behavior
A clear description of what you expected to happen.

### Actual Behavior
A clear description of what actually happened.

### Environment
- OS: [e.g. Ubuntu 22.04]
- Docker version: [e.g. 20.10.0]
- Docker Compose version: [e.g. 2.0.0]
- Database version: [e.g. MariaDB 10.5]
- Go version: [if running locally]

### Logs
```
Paste any relevant logs here
```

### Additional Context
Add any other context about the problem here.