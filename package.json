{"name": "vite-react-typescript-starter", "private": true, "version": "1.0.0", "type": "module", "scripts": {"start": "node server.js", "dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@types/react-router-dom": "^5.3.3", "cronstrue": "^2.57.0", "dotenv": "^16.4.7", "express": "^4.21.2", "http-proxy-middleware": "^3.0.3", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.4.0", "react-simple-pull-to-refresh": "^1.3.3", "react-swipeable": "^7.0.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@semantic-release/changelog": "^6.0.0", "@semantic-release/git": "^10.0.0", "@semantic-release/github": "^8.0.0", "@types/node": "^22.13.10", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.5.3", "semantic-release": "^21.0.0", "tailwindcss": "^3.4.17", "tailwindcss-scrollbar": "^0.1.0", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^6.2.2"}}