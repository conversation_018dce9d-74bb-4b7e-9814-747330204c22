.ptr {
  width: 100%;
  height: 100%;
  position: relative;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  touch-action: manipulation;
  overscroll-behavior-y: contain;
}

.ptr__children {
  width: 100%;
  height: 100%;
  overflow: visible;
  will-change: transform;
}

.ptr__pull-down {
  position: absolute;
  height: 48px;
  overflow: hidden;
  left: 0;
  right: 0;
  top: 0;
  z-index: -1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  transform: translateY(0);
  transition: transform 0.3s ease, opacity 0.3s ease;
  opacity: 0;
  pointer-events: none;
}

.ptr__loader {
  z-index: -1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  pointer-events: none;
}

.ptr--refreshing {
  overflow: hidden;
}

.ptr--reset {
  transition: transform 0.3s ease;
  transform: translateY(0) !important;
}
