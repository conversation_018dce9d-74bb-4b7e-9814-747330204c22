@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --toast-bg: #fff;
    --toast-color: #1f2937;
    --toast-shadow: 0 0 10px rgba(0, 0, 0, 0.1), 0 0 5px rgba(0, 0, 0, 0.05);
  }

  .dark {
    --toast-bg: #1f2937;
    --toast-color: #fff;
    --toast-shadow: 0 0 10px rgba(0, 0, 0, 0.3), 0 0 5px rgba(0, 0, 0, 0.15);
  }
}

input:focus,
textarea:focus,
select:focus {
  outline: none !important;
  border-color: rgb(180, 203, 236) !important;
  box-shadow: 0 0 0 1px rgb(209 213 219) !important;
}

.dark input:focus,
.dark textarea:focus,
.dark select:focus {
  border-color: rgb(69, 112, 172) !important;
  box-shadow: 0 0 0 1px rgb(75 85 99) !important;
}

input[type="number"] {
  appearance: textfield;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  appearance: none;
  margin: 0;
}

select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

.select-wrapper label {
  cursor: default;
  pointer-events: none;
}

.number-input-wrapper,
.select-wrapper {
  position: relative;
}

.number-input-controls,
.select-arrow {
  position: absolute;
  right: 0;
  top: 1px;
  height: calc(100% - 2px);
  display: flex;
  flex-direction: column;
  width: 2rem;
  cursor: pointer;
}

.number-input-up,
.number-input-down,
.select-arrow-icon {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
}

.select-arrow:hover .select-arrow-icon {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark .select-arrow:hover .select-arrow-icon {
  background-color: rgba(255, 255, 255, 0.1);
}

.select-wrapper select {
  pointer-events: auto;
  cursor: pointer;
}

label {
  pointer-events: none !important;
}

.select-wrapper * {
  pointer-events: none;
}

.select-wrapper select {
  pointer-events: auto;
}

select:hover {
  background-color: rgb(249 250 251) !important;
}

.dark select:hover {
  background-color: rgb(75 85 99) !important;
}

/* CRITICAL FIX: Date picker positioning override */
input[type="date"]:focus {
  position: fixed !important;
  z-index: 99999 !important;
  transform: none !important;
  will-change: auto !important;
}

input[type="date"]::-webkit-calendar-picker-indicator {
  position: fixed !important;
  z-index: 100000 !important;
  transform: none !important;
}

/* Override pull-to-refresh transforms for date picker */
.ptr__children input[type="date"]:focus,
.ptr__children .date-input-container input[type="date"]:focus {
  position: fixed !important;
  z-index: 99999 !important;
  transform: none !important;
}

/* Ensure calendar stays fixed */
.date-picker-fixed {
  position: fixed !important;
  z-index: 99999 !important;
  transform: none !important;
}
